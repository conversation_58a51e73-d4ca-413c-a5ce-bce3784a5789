{"name": "@zync-workspace/source", "version": "0.0.0", "license": "MIT", "scripts": {"zyncApp:start": "cd apps/zyncApp && react-native start", "zyncApp:ios": "cd apps/zyncApp && react-native run-ios", "zyncApp:android": "cd apps/zyncApp && react-native run-android", "zyncApp:build:android": "cd apps/zyncApp && cd android && ./gradlew assembleRelease", "zyncApp:build:android-bundle": "cd apps/zyncApp && cd android && ./gradlew bundleRelease", "zyncApp:build:ios": "cd apps/zyncApp && cd ios && xcodebuild -workspace ZyncApp.xcworkspace -scheme ZyncApp -configuration Release -destination generic/platform=iOS -archivePath ZyncApp.xcarchive archive", "zyncApp:clean:android": "cd apps/zyncApp && cd android && ./gradlew clean", "zyncApp:clean:ios": "cd apps/zyncApp && cd ios && xcodebuild clean -workspace ZyncApp.xcworkspace -scheme ZyncApp", "zyncApp:lint": "cd apps/zyncApp && eslint .", "zyncApp:test": "cd apps/zyncApp && jest"}, "private": true, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.2", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@react-navigation/stack": "^7.4.5", "axios": "^1.11.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-native": "0.80.2", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^3.19.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.28.2", "@nx/esbuild": "^21.3.11", "@nx/js": "^21.3.11", "@nx/node": "^21.3.11", "@nx/react-native": "^21.3.11", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "autoprefixer": "^10.4.21", "eslint": "^8.19.0", "jest": "^29.6.3", "nx": "21.3.11", "postcss": "^8.5.6", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "tailwindcss": "^3.4.17", "tslib": "^2.3.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "workspaces": ["packages/*"]}