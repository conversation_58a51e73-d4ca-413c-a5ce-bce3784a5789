{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-4c573e32d9bcd41bd76c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-92d552e2abffd1512809.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-26bd78fff101618f61a0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-305d1a35ddde4387ced1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-d579d1717447002b6ce8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-777ccc5e5e8bae470ee9.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-35a3bcab13a505d73d8e.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-a64d03f6d76a00d266b1.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-5e121fe90051c490e967.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-bc9159f8cb64497ef498.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-3449d92d4eb9dc33e328.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/ZyncAbid/zync/ZyncApp/android/app/.cxx/Debug/2g63565k/arm64-v8a", "source": "/Users/<USER>/ZyncAbid/zync/ZyncApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}