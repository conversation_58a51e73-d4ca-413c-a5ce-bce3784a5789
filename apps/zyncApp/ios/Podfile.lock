PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - fast_float (8.0.0)
  - FBLazyVector (0.80.2)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.80.2):
    - hermes-engine/Pre-built (= 0.80.2)
  - hermes-engine/Pre-built (0.80.2)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.80.2)
  - RCTRequired (0.80.2)
  - RCTTypeSafety (0.80.2):
    - FBLazyVector (= 0.80.2)
    - RCTRequired (= 0.80.2)
    - React-Core (= 0.80.2)
  - React (0.80.2):
    - React-Core (= 0.80.2)
    - React-Core/DevSupport (= 0.80.2)
    - React-Core/RCTWebSocket (= 0.80.2)
    - React-RCTActionSheet (= 0.80.2)
    - React-RCTAnimation (= 0.80.2)
    - React-RCTBlob (= 0.80.2)
    - React-RCTImage (= 0.80.2)
    - React-RCTLinking (= 0.80.2)
    - React-RCTNetwork (= 0.80.2)
    - React-RCTSettings (= 0.80.2)
    - React-RCTText (= 0.80.2)
    - React-RCTVibration (= 0.80.2)
  - React-callinvoker (0.80.2)
  - React-Core (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/CoreModulesHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/Default (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/DevSupport (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.2)
    - React-Core/RCTWebSocket (= 0.80.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTAnimationHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTBlobHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTImageHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTLinkingHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTNetworkHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTSettingsHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTTextHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTVibrationHeaders (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTWebSocket (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-CoreModules (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety (= 0.80.2)
    - React-Core/CoreModulesHeaders (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.80.2)
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.2)
    - React-debug (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.80.2)
    - React-perflogger (= 0.80.2)
    - React-runtimeexecutor (= 0.80.2)
    - React-timing (= 0.80.2)
    - SocketRocket
  - React-debug (0.80.2)
  - React-defaultsnativemodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-hermes
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - SocketRocket
  - React-domnativemodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.80.2)
    - React-Fabric/attributedstring (= 0.80.2)
    - React-Fabric/componentregistry (= 0.80.2)
    - React-Fabric/componentregistrynative (= 0.80.2)
    - React-Fabric/components (= 0.80.2)
    - React-Fabric/consistency (= 0.80.2)
    - React-Fabric/core (= 0.80.2)
    - React-Fabric/dom (= 0.80.2)
    - React-Fabric/imagemanager (= 0.80.2)
    - React-Fabric/leakchecker (= 0.80.2)
    - React-Fabric/mounting (= 0.80.2)
    - React-Fabric/observers (= 0.80.2)
    - React-Fabric/scheduler (= 0.80.2)
    - React-Fabric/telemetry (= 0.80.2)
    - React-Fabric/templateprocessor (= 0.80.2)
    - React-Fabric/uimanager (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/animations (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/attributedstring (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistry (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistrynative (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.80.2)
    - React-Fabric/components/root (= 0.80.2)
    - React-Fabric/components/scrollview (= 0.80.2)
    - React-Fabric/components/view (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/legacyviewmanagerinterop (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/root (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/scrollview (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/view (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric/consistency (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/core (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/dom (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/imagemanager (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/leakchecker (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/mounting (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers/events (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/scheduler (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/telemetry (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/templateprocessor (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager/consistency (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-FabricComponents (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.80.2)
    - React-FabricComponents/textlayoutmanager (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.80.2)
    - React-FabricComponents/components/iostextinput (= 0.80.2)
    - React-FabricComponents/components/modal (= 0.80.2)
    - React-FabricComponents/components/rncore (= 0.80.2)
    - React-FabricComponents/components/safeareaview (= 0.80.2)
    - React-FabricComponents/components/scrollview (= 0.80.2)
    - React-FabricComponents/components/text (= 0.80.2)
    - React-FabricComponents/components/textinput (= 0.80.2)
    - React-FabricComponents/components/unimplementedview (= 0.80.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/iostextinput (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/modal (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/rncore (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/safeareaview (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/scrollview (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/text (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/textinput (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricImage (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired (= 0.80.2)
    - RCTTypeSafety (= 0.80.2)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.80.2)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-featureflags (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-featureflagsnativemodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-graphics (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - SocketRocket
  - React-hermes (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.2)
    - React-jsi
    - React-jsiexecutor (= 0.80.2)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.2)
    - React-runtimeexecutor
    - SocketRocket
  - React-idlecallbacksnativemodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-ImageManager (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - SocketRocket
  - React-jserrorhandler (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - SocketRocket
  - React-jsi (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsiexecutor (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.2)
    - SocketRocket
  - React-jsinspector (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-perflogger (= 0.80.2)
    - React-runtimeexecutor (= 0.80.2)
    - SocketRocket
  - React-jsinspectorcdp (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsinspectornetwork (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsinspectorcdp
    - SocketRocket
  - React-jsinspectortracing (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-oscompat
    - SocketRocket
  - React-jsitooling (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - SocketRocket
  - React-jsitracing (0.80.2):
    - React-jsi
  - React-logger (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-Mapbuffer (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-microtasksnativemodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - react-native-safe-area-context (5.6.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.6.0)
    - react-native-safe-area-context/fabric (= 5.6.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/common (5.6.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/fabric (5.6.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-NativeModulesApple (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-oscompat (0.80.2)
  - React-perflogger (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-performancetimeline (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - SocketRocket
  - React-RCTActionSheet (0.80.2):
    - React-Core/RCTActionSheetHeaders (= 0.80.2)
  - React-RCTAnimation (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTAppDelegate (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - SocketRocket
  - React-RCTBlob (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTFabric (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-RCTFBReactNativeSpec (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
    - SocketRocket
  - React-RCTImage (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTLinking (0.80.2):
    - React-Core/RCTLinkingHeaders (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.80.2)
  - React-RCTNetwork (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTRuntime (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - SocketRocket
  - React-RCTSettings (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTText (0.80.2):
    - React-Core/RCTTextHeaders (= 0.80.2)
    - Yoga
  - React-RCTVibration (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-rendererconsistency (0.80.2)
  - React-renderercss (0.80.2):
    - React-debug
    - React-utils
  - React-rendererdebug (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-rncore (0.80.2)
  - React-RuntimeApple (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-RuntimeCore (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-hermes
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-runtimeexecutor (0.80.2):
    - React-jsi (= 0.80.2)
  - React-RuntimeHermes (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-utils
    - SocketRocket
  - React-runtimescheduler (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - SocketRocket
  - React-timing (0.80.2)
  - React-utils (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-hermes
    - React-jsi (= 0.80.2)
    - SocketRocket
  - ReactAppDependencyProvider (0.80.2):
    - ReactCodegen
  - ReactCodegen (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - ReactCommon (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - ReactCommon/turbomodule (= 0.80.2)
    - SocketRocket
  - ReactCommon/turbomodule (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.2)
    - React-cxxreact (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-logger (= 0.80.2)
    - React-perflogger (= 0.80.2)
    - ReactCommon/turbomodule/bridging (= 0.80.2)
    - ReactCommon/turbomodule/core (= 0.80.2)
    - SocketRocket
  - ReactCommon/turbomodule/bridging (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.2)
    - React-cxxreact (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-logger (= 0.80.2)
    - React-perflogger (= 0.80.2)
    - SocketRocket
  - ReactCommon/turbomodule/core (0.80.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.2)
    - React-cxxreact (= 0.80.2)
    - React-debug (= 0.80.2)
    - React-featureflags (= 0.80.2)
    - React-jsi (= 0.80.2)
    - React-logger (= 0.80.2)
    - React-perflogger (= 0.80.2)
    - React-utils (= 0.80.2)
    - SocketRocket
  - RNCAsyncStorage (2.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNGestureHandler (2.28.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNReanimated (3.19.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.19.1)
    - RNReanimated/worklets (= 3.19.1)
    - SocketRocket
    - Yoga
  - RNReanimated/reanimated (3.19.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.19.1)
    - SocketRocket
    - Yoga
  - RNReanimated/reanimated/apple (3.19.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNReanimated/worklets (3.19.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.19.1)
    - SocketRocket
    - Yoga
  - RNReanimated/worklets/apple (3.19.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNScreens (4.14.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.14.0)
    - SocketRocket
    - Yoga
  - RNScreens/common (4.14.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNVectorIcons (10.3.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../../../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../../../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../../../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../../../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../../../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../../../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../../../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../../../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../../../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../../../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../../../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../../../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../../../node_modules/react-native/React`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../../../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../../../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../../../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../../../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../../../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - RNGestureHandler (from `../../../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - RNVectorIcons (from `../../../node_modules/react-native-vector-icons`)
  - SocketRocket (~> 0.7.1)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-24-RNv0.80.2-5c7dbc0a78cb2d2a8bc81c41c617c3abecf209ff
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../../../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../../../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../../../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../../../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../../../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../../../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../../../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../../../node_modules/react-native/React"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../../../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../../../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNGestureHandler:
    :path: "../../../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../../../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: b32c788ed9c6a8c584d114d0047beda9664e7cc6
  FBLazyVector: 86588b5a1547e7a417942a08f49559b184e002c8
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  hermes-engine: bbc1152da7d2d40f9e59c28acc6576fcf5d28e2a
  RCT-Folly: 59ec0ac1f2f39672a0c6e6cecdd39383b764646f
  RCTDeprecation: 300c5eb91114d4339b0bb39505d0f4824d7299b7
  RCTRequired: e0446b01093475b7082fbeee5d1ef4ad1fe20ac4
  RCTTypeSafety: cb974efcdc6695deedf7bf1eb942f2a0603a063f
  React: e7a4655b09d0e17e54be188cc34c2f3e2087318a
  React-callinvoker: 62192daaa2f30c3321fc531e4f776f7b09cf892b
  React-Core: c400b068fdb6172177f3b3fae00c10d1077244d7
  React-CoreModules: 8e911a5a504b45824374eec240a78de7a6db8ca2
  React-cxxreact: 06a91f55ac5f842219d6ca47e0f77187a5b5f4ac
  React-debug: 1834225a63b420b16e9b8b01ba5870aee96d0610
  React-defaultsnativemodule: 260aa990a9617c58df46c00321f396ad6ea7cc7f
  React-domnativemodule: 9b3456a614c325da986867f27ca0eb34cb86828c
  React-Fabric: fc7bcbac28989e6025ca6ae0988bff61bb78e5d3
  React-FabricComponents: ae4a9c82bedf7c95bace1b215caf8685bcb32e23
  React-FabricImage: c9cd4786180c150bb2a3841d65d360fd52be9ef8
  React-featureflags: 534cd678e05848fbfc8c7288d4b14bcd8894b696
  React-featureflagsnativemodule: bf7419f4d81226a3c4dd792445a03a6d703ce9a4
  React-graphics: 18296c3559d54a42baaf7f2ae9c137a2e0fe9d51
  React-hermes: b6e33fcd21aa7523dc76e62acd7a547e68c28a5b
  React-idlecallbacksnativemodule: da8696a714ab16adb56bbfc9e0dfb4de7a713340
  React-ImageManager: 052ccce122e4fd4e09c5d4f30e56381704dac439
  React-jserrorhandler: 4c037384a32f57332abfa64181aeea915f9e0f0d
  React-jsi: 3fde19aaf675c0607a0824c4d6002a4943820fd9
  React-jsiexecutor: 4f898228240cf261a02568e985dfa7e1d7ad1dfb
  React-jsinspector: 4ad0cdfa25a45d1362e2ddd06c78727d7964b34f
  React-jsinspectorcdp: a649cc98a448e0fd8d54ac2a9e3e53177a1d8bd3
  React-jsinspectornetwork: 2d701b6b152be202342f8269223046ec664c7d47
  React-jsinspectortracing: cd898b3d7ea89f3e0ae10020fe3504bb4b327dd8
  React-jsitooling: feca163583c69ba642cebb6b8ccd2f5e6732fed8
  React-jsitracing: 1965307a468987b20d2a020f8fe782efa591ded7
  React-logger: ea80169d826e0cd112fa4d68f58b2b3b968f1ecb
  React-Mapbuffer: a5d550d1add940ed2bc65b20dc1413407bf1a63f
  React-microtasksnativemodule: 5d00fefc19f0bc9a6432e5533683d6fc9c3da4e1
  react-native-safe-area-context: b36ae404f4500dff1a0adc05ecfa66be319730ac
  React-NativeModulesApple: b22e6abb44d78270dfdfc7d85efe29e35e0333a7
  React-oscompat: 56d6de59f9ae95cd006a1c40be2cde83bc06a4e1
  React-perflogger: 0633844e495d8b34798c9bf0cb32ce315f1d5c9f
  React-performancetimeline: a04dae9154c32eda1891fcfa51cb2680a0421b3e
  React-RCTActionSheet: 49138012280ec3bbb35193d8d09adb8bc61c982e
  React-RCTAnimation: c7ed4a9d5a4e43c9b10f68bb43cd238c4a2e7e89
  React-RCTAppDelegate: ea2ab6f4aef1489f72025b7128d8ab645b40eafb
  React-RCTBlob: c052799460b245e1fffe3d1dddea36fa88e998a0
  React-RCTFabric: e7acf005f8ed58d09f755b980ff83703b3af9fcf
  React-RCTFBReactNativeSpec: ffb22c3ee3d359ae9245ca94af203845da9371ec
  React-RCTImage: 59fc2571f4f109a77139924f5babee8f9cd639c9
  React-RCTLinking: a045cb58c08188dce6c6f4621de105114b1b16ce
  React-RCTNetwork: fc7115a2f5e15ae0aa05e9a9be726817feefb482
  React-RCTRuntime: a7bca9be4f571586b2a9d4b57cf605421ffb6335
  React-RCTSettings: 30d7dd7eae66290467a1e72bf42d927fa78c3884
  React-RCTText: 755d59284e66c7d33bb4f0ccc428fe69110c3e74
  React-RCTVibration: ffe019e588815df226f6f8ccdc65979f8b2bc440
  React-rendererconsistency: d20fcb77173861cc7d8356239823e3b36966fc31
  React-renderercss: 63c720c32aaabd4788ac4136a071d49a052d8002
  React-rendererdebug: a25ddddc73cabf50d814d8dfbc60d257b3d854c4
  React-rncore: bafb76fc01b78757a9592e92dbc227f9260bf0ac
  React-RuntimeApple: 45f8ef1b220a91b4fa4a79820b81990bffd95aa5
  React-RuntimeCore: a0e095493b22ee3f6c639df4258cc5185674f0b8
  React-runtimeexecutor: b35de9cb7f5d19c66ea9b067235f95b947697ba5
  React-RuntimeHermes: 5b8126fffd1531475861dc0294a10b5f9793271a
  React-runtimescheduler: 44fa97351d105afd0ffaecc4ed11cadad562deb6
  React-timing: 4f97958cc918f0af9444f93e4a7083415e6f5daf
  React-utils: 3c4b0b7788e4dc132d1bf918bc0615e2b21f36b3
  ReactAppDependencyProvider: 6c9197c1f6643633012ab646d2bfedd1b0d25989
  ReactCodegen: 53a00538c1ed89ad06aa6da85f1633d3fda25dec
  ReactCommon: 7aca047f2f453a7d7f0adeccb63810d61829235a
  RNCAsyncStorage: 767abb068db6ad28b5f59a129fbc9fab18b377e2
  RNGestureHandler: cdca641e24d0ab743dcd90a24de4e6259e6aa0de
  RNReanimated: 3ad140a29b1a52481c56b55f526a1b2529859494
  RNScreens: 9bc9238d83938b5c22d2b90fdd45ade96495bbf6
  RNVectorIcons: 54df27a2e90ddeb674c7237d76060ec9762d0bc5
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: a742cc68e8366fcfc681808162492bc0aa7a9498

PODFILE CHECKSUM: 93d20cd6264dc9e69b3b13679519f030fce46484

COCOAPODS: 1.15.2
