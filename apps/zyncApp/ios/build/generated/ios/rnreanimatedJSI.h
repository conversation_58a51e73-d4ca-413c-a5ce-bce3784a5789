/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeReanimatedModuleCxxSpecJSI : public TurboModule {
protected:
  NativeReanimatedModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual bool installTurboModule(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeReanimatedModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ReanimatedModule";

protected:
  NativeReanimatedModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeReanimatedModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeReanimatedModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeReanimatedModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    bool installTurboModule(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::installTurboModule) == 1,
          "Expected installTurboModule(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::installTurboModule, jsInvoker_, instance_);
    }

  private:
    friend class NativeReanimatedModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeWorkletsModuleCxxSpecJSI : public TurboModule {
protected:
  NativeWorkletsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual bool installTurboModule(jsi::Runtime &rt, jsi::String valueUnpackerCode) = 0;

};

template <typename T>
class JSI_EXPORT NativeWorkletsModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "WorkletsModule";

protected:
  NativeWorkletsModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeWorkletsModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeWorkletsModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeWorkletsModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    bool installTurboModule(jsi::Runtime &rt, jsi::String valueUnpackerCode) override {
      static_assert(
          bridging::getParameterCount(&T::installTurboModule) == 2,
          "Expected installTurboModule(...) to have 2 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::installTurboModule, jsInvoker_, instance_, std::move(valueUnpackerCode));
    }

  private:
    friend class NativeWorkletsModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
