/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#import "rngesturehandler_codegen.h"


@implementation NativeRNGestureHandlerModuleSpecBase


- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper
{
  _eventEmitterCallback = std::move(eventEmitterCallbackWrapper->_eventEmitterCallback);
}
@end


namespace facebook::react {
  
    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_handleSetJSResponder(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "handleSetJSResponder", @selector(handleSetJSResponder:blockNativeResponder:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_handleClearJSResponder(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "handleClearJSResponder", @selector(handleClearJSResponder), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_createGestureHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "createGestureHandler", @selector(createGestureHandler:handlerTag:config:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_attachGestureHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "attachGestureHandler", @selector(attachGestureHandler:newView:actionType:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_updateGestureHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "updateGestureHandler", @selector(updateGestureHandler:newConfig:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_dropGestureHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "dropGestureHandler", @selector(dropGestureHandler:), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_install(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, BooleanKind, "install", @selector(install), args, count);
    }

    static facebook::jsi::Value __hostFunction_NativeRNGestureHandlerModuleSpecJSI_flushOperations(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "flushOperations", @selector(flushOperations), args, count);
    }

  NativeRNGestureHandlerModuleSpecJSI::NativeRNGestureHandlerModuleSpecJSI(const ObjCTurboModule::InitParams &params)
    : ObjCTurboModule(params) {
      
        methodMap_["handleSetJSResponder"] = MethodMetadata {2, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_handleSetJSResponder};
        
        
        methodMap_["handleClearJSResponder"] = MethodMetadata {0, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_handleClearJSResponder};
        
        
        methodMap_["createGestureHandler"] = MethodMetadata {3, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_createGestureHandler};
        
        
        methodMap_["attachGestureHandler"] = MethodMetadata {3, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_attachGestureHandler};
        
        
        methodMap_["updateGestureHandler"] = MethodMetadata {2, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_updateGestureHandler};
        
        
        methodMap_["dropGestureHandler"] = MethodMetadata {1, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_dropGestureHandler};
        
        
        methodMap_["install"] = MethodMetadata {0, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_install};
        
        
        methodMap_["flushOperations"] = MethodMetadata {0, __hostFunction_NativeRNGestureHandlerModuleSpecJSI_flushOperations};
        
  }
} // namespace facebook::react
