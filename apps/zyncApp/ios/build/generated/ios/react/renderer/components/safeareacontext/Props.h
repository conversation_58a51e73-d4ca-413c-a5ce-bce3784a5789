
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GeneratePropsH.js
 */
#pragma once

#include <react/renderer/components/view/ViewProps.h>
#include <react/renderer/core/PropsParserContext.h>
#include <react/renderer/core/propsConversions.h>

namespace facebook::react {

class RNCSafeAreaProviderProps final : public ViewProps {
 public:
  RNCSafeAreaProviderProps() = default;
  RNCSafeAreaProviderProps(const PropsParserContext& context, const RNCSafeAreaProviderProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  
};

enum class RNCSafeAreaViewMode { Padding, Margin };

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCSafeAreaViewMode &result) {
  auto string = (std::string)value;
  if (string == "padding") { result = RNCSafeAreaViewMode::Padding; return; }
  if (string == "margin") { result = RNCSafeAreaViewMode::Margin; return; }
  abort();
}

static inline std::string toString(const RNCSafeAreaViewMode &value) {
  switch (value) {
    case RNCSafeAreaViewMode::Padding: return "padding";
    case RNCSafeAreaViewMode::Margin: return "margin";
  }
}
struct RNCSafeAreaViewEdgesStruct {
  std::string top{};
  std::string right{};
  std::string bottom{};
  std::string left{};
};

static inline void fromRawValue(const PropsParserContext& context, const RawValue &value, RNCSafeAreaViewEdgesStruct &result) {
  auto map = (std::unordered_map<std::string, RawValue>)value;

  auto tmp_top = map.find("top");
  if (tmp_top != map.end()) {
    fromRawValue(context, tmp_top->second, result.top);
  }
  auto tmp_right = map.find("right");
  if (tmp_right != map.end()) {
    fromRawValue(context, tmp_right->second, result.right);
  }
  auto tmp_bottom = map.find("bottom");
  if (tmp_bottom != map.end()) {
    fromRawValue(context, tmp_bottom->second, result.bottom);
  }
  auto tmp_left = map.find("left");
  if (tmp_left != map.end()) {
    fromRawValue(context, tmp_left->second, result.left);
  }
}

static inline std::string toString(const RNCSafeAreaViewEdgesStruct &value) {
  return "[Object RNCSafeAreaViewEdgesStruct]";
}
class RNCSafeAreaViewProps final : public ViewProps {
 public:
  RNCSafeAreaViewProps() = default;
  RNCSafeAreaViewProps(const PropsParserContext& context, const RNCSafeAreaViewProps &sourceProps, const RawProps &rawProps);

#pragma mark - Props

  RNCSafeAreaViewMode mode{RNCSafeAreaViewMode::Padding};
  RNCSafeAreaViewEdgesStruct edges{};
};

} // namespace facebook::react
