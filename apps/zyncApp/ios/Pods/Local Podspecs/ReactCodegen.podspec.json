{"name": "ReactCodegen", "version": "0.80.2", "summary": "Temp pod for generated files for React Native", "homepage": "https://facebook.com/", "license": "Unlicense", "authors": "Facebook", "compiler_flags": "-DFOLLY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DF<PERSON><PERSON>Y_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -Wno-nullability-completeness -std=c++20", "source": {"git": ""}, "header_mappings_dir": "./", "platforms": {"ios": "15.1"}, "source_files": "**/*.{h,mm,cpp}", "exclude_files": "RCTAppDependencyProvider.{h,mm}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"$(PODS_ROOT)/ReactNativeDependencies\"", "\"${PODS_ROOT}/Headers/Public/ReactCodegen/react/renderer/components\"", "\"$(PODS_ROOT)/Headers/Private/React-Fabric\"", "\"$(PODS_ROOT)/Headers/Private/React-RCTFabric\"", "\"$(PODS_ROOT)/Headers/Private/Yoga\"", "\"$(PODS_TARGET_SRCROOT)\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "FRAMEWORK_SEARCH_PATHS": [], "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation"}, "dependencies": {"React-jsiexecutor": [], "RCTRequired": [], "RCTTypeSafety": [], "React-Core": [], "React-jsi": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "React-graphics": [], "React-rendererdebug": [], "React-Fabric": [], "React-FabricImage": [], "React-debug": [], "React-utils": [], "React-featureflags": [], "React-RCTAppDelegate": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}, "script_phases": {"name": "Generate Specs", "execution_position": "before_compile", "input_files": [], "show_env_vars_in_log": true, "output_files": ["${DERIVED_FILE_DIR}/react-codegen.log"], "script": "pushd \"$PODS_ROOT/../\" > /dev/null\nRCT_SCRIPT_POD_INSTALLATION_ROOT=$(pwd)\npopd >/dev/null\n\nexport RCT_SCRIPT_RN_DIR=\"$RCT_SCRIPT_POD_INSTALLATION_ROOT/../../../node_modules/react-native\"\nexport RCT_SCRIPT_APP_PATH=\"$RCT_SCRIPT_POD_INSTALLATION_ROOT/../../..\"\nexport RCT_SCRIPT_OUTPUT_DIR=\"$RCT_SCRIPT_POD_INSTALLATION_ROOT\"\nexport RCT_SCRIPT_TYPE=\"withCodegenDiscovery\"\n\nSCRIPT_PHASES_SCRIPT=\"$RCT_SCRIPT_RN_DIR/scripts/react_native_pods_utils/script_phases.sh\"\nWITH_ENVIRONMENT=\"$RCT_SCRIPT_RN_DIR/scripts/xcode/with-environment.sh\"\n/bin/sh -c \"$WITH_ENVIRONMENT $SCRIPT_PHASES_SCRIPT\"\n"}}