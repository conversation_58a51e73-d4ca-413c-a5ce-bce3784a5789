# ZyncApp Build Guide

This guide will help you create builds for both iOS and Android platforms.

## Prerequisites

### For Android:
- Android Studio installed
- Android SDK installed
- Java Development Kit (JDK) 17 or higher
- Environment variables set (ANDROID_HOME, JAVA_HOME)

### For iOS:
- macOS (required for iOS builds)
- Xcode installed (latest version recommended)
- iOS Developer Account (for App Store distribution)
- CocoaPods installed

## Build Commands

### Android Builds

#### 1. Debug Build (for testing)
```bash
npm run zyncApp:android
```

#### 2. Release APK Build
```bash
npm run zyncApp:build:android
```
This creates an APK file at: `apps/zyncApp/android/app/build/outputs/apk/release/app-release.apk`

#### 3. Release AAB Build (for Google Play Store)
```bash
npm run zyncApp:build:android-bundle
```
This creates an AAB file at: `apps/zyncApp/android/app/build/outputs/bundle/release/app-release.aab`

#### 4. Clean Android Build
```bash
npm run zyncApp:clean:android
```

### iOS Builds

#### 1. Debug Build (for testing)
```bash
npm run zyncApp:ios
```

#### 2. Release Build for Device
```bash
npm run zyncApp:build:ios
```

#### 3. Release Build for Simulator
```bash
npm run zyncApp:build:ios-simulator
```

#### 4. Clean iOS Build
```bash
npm run zyncApp:clean:ios
```

## Step-by-Step Build Process

### Android Build Process

1. **Prepare the environment:**
   ```bash
   # Make sure you're in the project root
   cd /Users/<USER>/ZyncAbid/zync/zync-workspace

   # Install dependencies
   npm install
   ```

2. **Clean previous builds:**
   ```bash
   npm run zyncApp:clean:android
   ```

3. **Create release build:**
   ```bash
   # For APK
   npm run zyncApp:build:android

   # For AAB (recommended for Play Store)
   npm run zyncApp:build:android-bundle
   ```

4. **Find your build files:**
   - APK: `apps/zyncApp/android/app/build/outputs/apk/release/app-release.apk`
   - AAB: `apps/zyncApp/android/app/build/outputs/bundle/release/app-release.aab`

### iOS Build Process

1. **Prepare the environment:**
   ```bash
   # Make sure you're in the project root
   cd /Users/<USER>/ZyncAbid/zync/zync-workspace

   # Install dependencies
   npm install

   # Install iOS dependencies
   cd apps/zyncApp/ios && pod install && cd ../../..
   ```

2. **Clean previous builds:**
   ```bash
   npm run zyncApp:clean:ios
   ```

3. **Create release build:**
   ```bash
   npm run zyncApp:build:ios
   ```

4. **Find your build files:**
   - Archive: `apps/zyncApp/ios/ZyncApp.xcarchive`

## Important Notes

### Android Signing
- Currently using debug keystore for release builds
- For production, you need to create a release keystore
- Update `android/app/build.gradle` with your release keystore configuration

### iOS Signing
- You need a valid iOS Developer Account
- Configure signing in Xcode
- Set up proper provisioning profiles

### Build Optimization
- Enable ProGuard for Android release builds by setting `enableProguardInReleaseBuilds = true` in `android/app/build.gradle`
- Use Hermes engine for better performance (already enabled by default)

## Troubleshooting

### Common Android Issues:
1. **Gradle build fails:**
   ```bash
   cd apps/zyncApp/android && ./gradlew clean && cd ../../..
   npm run zyncApp:build:android
   ```

2. **Metro bundler issues:**
   ```bash
   npx react-native start --reset-cache
   ```

### Common iOS Issues:
1. **Pod install fails:**
   ```bash
   cd apps/zyncApp/ios && pod deintegrate && pod install && cd ../../..
   ```

2. **Xcode build fails:**
   - Clean build folder in Xcode
   - Delete derived data: `~/Library/Developer/Xcode/DerivedData`

## Distribution

### Android:
- APK: Direct installation on devices
- AAB: Upload to Google Play Console

### iOS:
- Archive: Upload to App Store Connect via Xcode
- Or create IPA for TestFlight distribution

## Environment Setup Verification

Run these commands to verify your setup:

```bash
# Check React Native CLI
npx react-native --version

# Check Android setup
npx react-native doctor

# Check iOS setup (macOS only)
npx react-native doctor
``` 